<template>
	<div class="modern-app">
		<!-- 主内容区域 -->
		<div class="app-main">
			<!-- 左侧结构树面板 -->
			<div
				class="sidebar-container left-sidebar"
				:class="{ collapsed: isSidebarCollapsed }"
			>
				<div
					class="sidebar-header"
					v-show="!isSidebarCollapsed"
				>
					<h3 class="sidebar-title">
						<el-icon><Grid /></el-icon>
						场景结构
					</h3>
					<div class="sidebar-actions">
						<el-button
							size="small"
							text
							@click="toggleSidebar"
							class="toggle-btn"
						>
							<el-icon><DCaret /></el-icon>
						</el-button>
					</div>
				</div>
				<!-- 收起状态的标题栏 -->
				<div
					class="sidebar-header-collapsed"
					v-show="isSidebarCollapsed"
					@click="toggleSidebar"
				>
					<el-icon class="collapsed-toggle-icon"><DArrowRight /></el-icon>
				</div>
				<div class="sidebar-content">
					<StructureTree
						ref="structureTreeRef"
						:person-data="personData"
						:fds-data="fdsData"
						:collapsed="isSidebarCollapsed"
						:selected-node="selectedNode"
						:hidden-nodes="hiddenNodes"
						@node-click="handleNodeClick"
						@toggle-node-visibility="handleToggleNodeVisibility"
					/>
				</div>
			</div>

			<!-- 中央3D场景区域 -->
			<div class="scene-container">
				<div class="scene-viewport">
					<Scene3D
						ref="scene3DRef"
						@scene-loaded="handleSceneLoaded"
						@object-click="handleObjectClick"
					/>
					<!-- 播放控制面板 - 添加到场景视口底部 -->
					<PlaybackControls
						v-if="isSceneLoaded && characterManager"
						:character-manager="characterManager"
					/>
				</div>
			</div>

			<!-- 右侧控制面板 -->
			<div
				class="sidebar-container right-sidebar"
				:class="{ collapsed: isControlSidebarCollapsed }"
			>
				<div
					class="sidebar-header"
					v-show="!isControlSidebarCollapsed"
				>
					<h3 class="sidebar-title">
						<el-icon><Setting /></el-icon>
						控制面板
					</h3>
					<div class="sidebar-actions">
						<el-button
							size="small"
							text
							@click="toggleControlSidebar"
							class="toggle-btn"
						>
							<el-icon><DCaret /></el-icon>
						</el-button>
					</div>
				</div>
				<!-- 收起状态的标题栏 -->
				<div
					class="sidebar-header-collapsed"
					v-show="isControlSidebarCollapsed"
					@click="toggleControlSidebar"
				>
					<el-icon class="collapsed-toggle-icon"><DArrowLeft /></el-icon>
				</div>
				<div class="sidebar-content">
					<!-- 场景控制面板 -->
					<ControlPanel
						v-if="isSceneLoaded"
						:is-collapsed="isControlSidebarCollapsed"
						:view-engine="viewEngine"
						:material-transparency="materialTransparency"
						@camera-reset="handleCameraReset"
						@material-transparency-change="handleMaterialTransparencyChange"
						@ambient-light-change="handleAmbientLightChange"
						@ambient-light-color-change="handleAmbientLightColorChange"
						@directional-light-change="handleDirectionalLightChange"
						@directional-light-color-change="handleDirectionalLightColorChange"
						@directional-light-position-change="
							handleDirectionalLightPositionChange
						"
					/>
				</div>
			</div>
		</div>

		<!-- 全局Loading组件 -->
		<LoadingOverlay
			:visible="loadingState.visible"
			:title="loadingState.title"
			:steps="loadingState.steps"
			:current-step-index="loadingState.currentStepIndex"
			:step-progress="loadingState.stepProgress"
			:status-text="loadingState.statusText"
		/>

		<!-- 快捷键提示 -->
		<div
			class="shortcuts-panel"
			v-if="showShortcuts"
		>
			<div class="shortcuts-content glass-panel">
				<h4>快捷键</h4>
				<div class="shortcut-list">
					<div class="shortcut-item">
						<kbd>R</kbd>
						<span>重置视角</span>
					</div>
					<div class="shortcut-item">
						<kbd>Space</kbd>
						<span>播放/暂停</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { ref, onMounted, onUnmounted, shallowRef } from 'vue';
	import {
		Grid,
		DCaret,
		DArrowRight,
		Check,
		Loading,
		Setting,
		DArrowLeft,
	} from '@element-plus/icons-vue';
	import StructureTree from './components/StructureTree.vue';
	import Scene3D from './components/Scene3D.vue';
	import LoadingOverlay from './components/LoadingOverlay.vue';
	import ControlPanel from './components/ControlPanel.vue';
	import PlaybackControls from './components/PlaybackControls.vue';
	import { globalLoading } from './composables/useLoading.js';

	const scene3DRef = ref(null);
	const structureTreeRef = ref(null);
	const personData = ref([]);
	const fdsData = shallowRef([]);
	const fdsDataLength = ref(0);
	const isSceneLoaded = ref(false);
	const showShortcuts = ref(false);
	const isSidebarCollapsed = ref(false);
	const isControlSidebarCollapsed = ref(false);
	const selectedNode = ref(null);
	const hiddenNodes = ref([]);
	const characterManager = ref(null);
	const viewEngine = ref(null);
	const materialTransparency = ref(0.5);

	const { loadingState } = globalLoading;

	// 处理结构树节点点击事件
	const handleNodeClick = (nodeData) => {
		console.log('App.vue handleNodeClick 被调用，nodeData:', nodeData);
		selectedNode.value = nodeData;

		// 如果点击的是具体的人员节点，显示人员详情面板
		if (nodeData.type === 'person') {
			console.log('检测到人员节点点击，准备显示详情面板');
			if (scene3DRef.value && nodeData.data) {
				console.log(
					'调用 scene3DRef.value.showPersonDetail()，数据:',
					nodeData.data
				);
				scene3DRef.value.showPersonDetail(nodeData.data);
				// 同时执行高亮
				scene3DRef.value.focusOnObject(nodeData);
			} else {
				console.log('scene3DRef.value 为空或 nodeData.data 为空');
			}
		} else {
			// 其他节点正常处理高亮
			console.log('其他类型节点，执行高亮');
			if (scene3DRef.value) {
				scene3DRef.value.focusOnObject(nodeData);
			}
		}
	};

	// 处理3D对象点击事件
	const handleObjectClick = (nodeData) => {
		console.log('3D对象点击事件，nodeData:', nodeData);

		selectedNode.value = {
			id: nodeData.id,
			label: nodeData.label,
			type: nodeData.type,
			data: nodeData.data,
			groupId: nodeData.groupId,
			groupLabel: nodeData.groupLabel,
		};

		// 如果点击的是人物模型，显示人员详情面板
		if (nodeData.type === 'person' && nodeData.data) {
			console.log('检测到人物模型点击，准备显示详情面板');
			if (scene3DRef.value) {
				console.log(
					'调用 scene3DRef.value.showPersonDetail()，数据:',
					nodeData.data
				);
				scene3DRef.value.showPersonDetail(nodeData.data);
			}
		} else {
			// 对于非人物对象，展开侧边栏显示详情
			if (isSidebarCollapsed.value) {
				isSidebarCollapsed.value = false;
			}
		}
	};

	// 处理切换节点可见性（高性能版本）
	const handleToggleNodeVisibility = (nodeData) => {
		if (!scene3DRef.value) return;

		// 注意：StructureTree已经更新了自己的状态，所以我们需要反向操作3D场景
		// 如果StructureTree显示为隐藏，说明刚刚从显示变为隐藏，需要隐藏3D对象
		// 如果StructureTree显示为显示，说明刚刚从隐藏变为显示，需要显示3D对象

		if (nodeData.type === 'fds_object') {
			// 单个对象切换
			const isNowHidden = structureTreeRef.value?.hiddenNodesSet?.has(
				nodeData.id
			);

			if (isNowHidden) {
				// 现在是隐藏状态，执行隐藏操作
				scene3DRef.value.hideObjectById(nodeData.id);
			} else {
				// 现在是显示状态，执行显示操作
				scene3DRef.value.showObjectById(nodeData.id);
			}
		} else if (nodeData.type === 'fds_group') {
			// 分组切换 - 获取组内所有对象并同步状态
			const groupObjects = collectLeafNodesFromNodeData(nodeData);

			groupObjects.forEach((obj) => {
				const isNowHidden = structureTreeRef.value?.hiddenNodesSet?.has(obj.id);

				if (isNowHidden) {
					scene3DRef.value.hideObjectById(obj.id);
				} else {
					scene3DRef.value.showObjectById(obj.id);
				}
			});
		} else if (nodeData.type === 'fds_root') {
			// 根节点切换 - 获取所有FDS对象并同步状态
			const allFdsObjects = fdsData.value || [];

			allFdsObjects.forEach((obj) => {
				const isNowHidden = structureTreeRef.value?.hiddenNodesSet?.has(
					obj._id
				);

				if (isNowHidden) {
					scene3DRef.value.hideObjectById(obj._id);
				} else {
					scene3DRef.value.showObjectById(obj._id);
				}
			});
		}
	};

	// 辅助方法：从节点数据收集叶子节点
	const collectLeafNodesFromNodeData = (nodeData) => {
		if (nodeData.type === 'fds_group') {
			// 从fdsData中筛选出属于该组的对象，并转换为树节点格式
			return (fdsData.value || [])
				.filter(
					(obj) =>
						obj.groupId === nodeData.id ||
						(obj.ID && obj.ID.startsWith(nodeData.label))
				)
				.map((obj) => ({
					id: obj._id, // 使用_id作为树节点的id
					type: 'fds_object',
					data: obj,
				}));
		}
		return [];
	};

	// 隐藏单个对象（测试版本 - 不更新响应式数据）
	const hideIndividualObject = (nodeData) => {
		if (!scene3DRef.value) return;

		const success = scene3DRef.value.hideObjectById(nodeData.id);
		if (success) {
			console.log(`已隐藏对象: ${nodeData.label}`);
		}
	};

	// 隐藏组内所有对象（测试版本 - 不更新响应式数据）
	const hideGroupObjects = (groupData) => {
		if (!scene3DRef.value) return;

		console.log('hideGroupObjects调用，groupData:', groupData);
		console.log('groupData.id:', groupData.id);
		console.log('groupData.label:', groupData.label);
		console.log('groupData.type:', groupData.type);

		let hiddenIds = [];

		if (groupData.type === 'fds_root') {
			// 隐藏所有FDS对象
			hiddenIds = scene3DRef.value.hideAllFdsObjects();
		} else if (groupData.type === 'fds_group') {
			// 隐藏特定组
			hiddenIds = scene3DRef.value.hideObjectsByGroupId(groupData.id);
		}

		console.log(`已隐藏组: ${groupData.label} (${hiddenIds.length}个对象)`);
	};

	// 显示对象或组
	const showObject = (nodeData) => {
		if (!scene3DRef.value) return;

		try {
			if (nodeData.type === 'fds_object') {
				// 显示单个FDS对象
				showIndividualObject(nodeData);
			} else if (
				nodeData.type === 'fds_group' ||
				nodeData.type === 'fds_root'
			) {
				// 显示组或根节点下的所有隐藏对象
				showGroupObjects(nodeData);
			}
		} catch (error) {
			console.error('显示对象时出错:', error);
		}
	};

	// 显示单个对象（测试版本 - 不更新响应式数据）
	const showIndividualObject = (nodeData) => {
		if (!scene3DRef.value) return;

		const success = scene3DRef.value.showObjectById(nodeData.id);
		if (success) {
			console.log(`已显示对象: ${nodeData.label}`);
		}
	};

	// 显示组内所有隐藏对象（测试版本 - 不更新响应式数据）
	const showGroupObjects = (groupData) => {
		if (!scene3DRef.value) return;

		let shownIds = [];

		if (groupData.type === 'fds_root') {
			// 显示所有隐藏的FDS对象
			shownIds = scene3DRef.value.showAllFdsObjects();
		} else if (groupData.type === 'fds_group') {
			// 显示特定组的隐藏对象
			const groupId = groupData.id;
			shownIds = scene3DRef.value.showObjectsByGroupId(groupId);
		}

		console.log(`已显示组: ${groupData.label} (${shownIds.length}个对象)`);
	};

	// 辅助方法：获取所有分组ID
	const getAllGroupIds = () => {
		const groupIds = new Set();
		if (fdsData.value && Array.isArray(fdsData.value)) {
			fdsData.value.forEach((item) => {
				if (item.groupId) {
					groupIds.add(item.groupId);
				}
			});
		}
		return Array.from(groupIds);
	};

	// 辅助方法：根据ID查找节点信息
	const findNodeInfoById = (id) => {
		if (fdsData.value && Array.isArray(fdsData.value)) {
			const item = fdsData.value.find((item) => item._id === id);
			if (item) {
				return {
					label: item.ID,
					type: 'fds_object',
				};
			}
		}
		return null;
	};

	// 处理场景加载完成
	const handleSceneLoaded = () => {
		console.log('App.vue: 场景加载完成');
		isSceneLoaded.value = true;
		if (scene3DRef.value) {
			characterManager.value = scene3DRef.value.getCharacterManager();
			viewEngine.value = scene3DRef.value.getViewEngine();
			console.log('App.vue: 获取到的 viewEngine:', viewEngine.value);
			// 设置场景为较暗的亮度
			scene3DRef.value.setSceneBrightness('dark');
			// 应用默认透明度
			if (viewEngine.value && viewEngine.value.setFdsTransparency) {
				viewEngine.value.setFdsTransparency(materialTransparency.value);
			}
		}
	};

	// 处理材质透明度变化
	const handleMaterialTransparencyChange = (value) => {
		console.log('App.vue: 材质透明度变化', value);
		materialTransparency.value = value;
		if (viewEngine.value && viewEngine.value.setFdsTransparency) {
			console.log('App.vue: 调用 setFdsTransparency');
			viewEngine.value.setFdsTransparency(value);
		} else {
			console.log('App.vue: viewEngine 或 setFdsTransparency 方法不存在', {
				viewEngine: !!viewEngine.value,
				setFdsTransparency: !!(
					viewEngine.value && viewEngine.value.setFdsTransparency
				),
			});
		}
	};

	// 处理环境光强度变化
	const handleAmbientLightChange = (value) => {
		console.log('App.vue: 环境光强度变化', value);
		if (viewEngine.value && viewEngine.value.setAmbientLightIntensity) {
			viewEngine.value.setAmbientLightIntensity(value);
		}
	};

	// 处理环境光颜色变化
	const handleAmbientLightColorChange = (value) => {
		console.log('App.vue: 环境光颜色变化', value);
		if (viewEngine.value && viewEngine.value.setAmbientLightColor) {
			viewEngine.value.setAmbientLightColor(value);
		}
	};

	// 处理平行光强度变化
	const handleDirectionalLightChange = (value) => {
		console.log('App.vue: 平行光强度变化', value);
		if (viewEngine.value && viewEngine.value.setDirectionalLightIntensity) {
			viewEngine.value.setDirectionalLightIntensity(value);
		}
	};

	// 处理平行光颜色变化
	const handleDirectionalLightColorChange = (value) => {
		console.log('App.vue: 平行光颜色变化', value);
		if (viewEngine.value && viewEngine.value.setDirectionalLightColor) {
			viewEngine.value.setDirectionalLightColor(value);
		}
	};

	// 处理平行光位置变化
	const handleDirectionalLightPositionChange = (position) => {
		console.log('App.vue: 平行光位置变化', position);
		if (viewEngine.value && viewEngine.value.setDirectionalLightPosition) {
			viewEngine.value.setDirectionalLightPosition(
				position.x,
				position.y,
				position.z
			);
		}
	};

	// 切换侧边栏
	const toggleSidebar = () => {
		isSidebarCollapsed.value = !isSidebarCollapsed.value;
	};

	// 切换控制面板侧边栏
	const toggleControlSidebar = () => {
		isControlSidebarCollapsed.value = !isControlSidebarCollapsed.value;
	};

	// 重置视图
	const resetView = () => {
		if (scene3DRef.value) {
			scene3DRef.value.resetCamera();
		}
	};

	// 键盘快捷键处理
	const handleKeydown = (event) => {
		switch (event.key.toLowerCase()) {
			case 'r':
				if (!event.ctrlKey && !event.metaKey) {
					event.preventDefault();
					resetView();
				}
				break;
			case ' ':
				if (!event.ctrlKey && !event.metaKey) {
					event.preventDefault();
					if (characterManager.value) {
						if (characterManager.value.isPlaying) {
							characterManager.value.pause();
						} else {
							characterManager.value.play();
						}
					}
				}
				break;
			case '?':
			case 'h':
				if (!event.ctrlKey && !event.metaKey) {
					event.preventDefault();
					showShortcuts.value = !showShortcuts.value;
				}
				break;
			case 'escape':
				showShortcuts.value = false;
				break;
		}
	};

	// 处理相机重置
	const handleCameraReset = () => {
		if (scene3DRef.value) {
			scene3DRef.value.resetCamera();
		}
	};

	onMounted(async () => {
		document.addEventListener('keydown', handleKeydown);
		if (scene3DRef.value) {
			await scene3DRef.value.initialize();
			personData.value = scene3DRef.value.getPersonData();
			fdsData.value = scene3DRef.value.getFdsData();
			fdsDataLength.value = fdsData.value.length;
			characterManager.value = scene3DRef.value.getCharacterManager();
			isSceneLoaded.value = true;
		}
	});

	onUnmounted(() => {
		document.removeEventListener('keydown', handleKeydown);
	});
</script>

<style scoped>
	.modern-app {
		width: 100vw;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background: var(--app-bg-primary);
		color: var(--app-text-primary);
		overflow: hidden;
	}

	.app-main {
		flex: 1;
		display: flex;
		overflow: hidden;
		min-height: 0;
		height: 100vh;
	}

	.sidebar-container {
		width: 320px;
		background: var(--app-surface-1);
		display: flex;
		flex-direction: column;
		transition: all var(--app-duration-slow) var(--app-ease-out);
		flex-shrink: 0;
	}

	.left-sidebar {
		border-right: 1px solid var(--app-border-primary);
	}

	.right-sidebar {
		border-left: 1px solid var(--app-border-primary);
	}

	.sidebar-container.collapsed {
		width: 60px;
	}

	.sidebar-header {
		height: 56px;
		padding: 0 var(--app-space-md);
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--app-border-primary);
		background: var(--app-surface-2);
	}

	.sidebar-header-collapsed {
		height: 56px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-bottom: 1px solid var(--app-border-primary);
		background: var(--app-surface-2);
		cursor: pointer;
		transition: all var(--app-duration-normal) var(--app-ease-out);
	}

	.sidebar-header-collapsed:hover {
		background: var(--app-surface-3);
	}

	.collapsed-toggle-icon {
		font-size: 16px;
		color: var(--app-primary);
		transition: transform var(--app-duration-normal);
	}

	.sidebar-header-collapsed:hover .collapsed-toggle-icon {
		transform: translateX(2px);
	}

	.sidebar-title {
		margin: 0;
		font-size: var(--app-font-size-base);
		font-weight: var(--app-font-weight-semibold);
		color: var(--app-text-primary);
		display: flex;
		align-items: center;
		gap: var(--app-space-sm);
	}

	.toggle-btn {
		padding: var(--app-space-xs);
		border-radius: var(--app-radius-small);
		transition: all var(--app-duration-normal) var(--app-ease-out);
	}

	.sidebar-content {
		flex: 1;
		overflow: hidden;
	}

	.scene-container {
		flex: 1;
		display: flex;
		flex-direction: column;
		overflow: hidden;
	}

	.scene-viewport {
		flex: 1;
		position: relative;
		overflow: hidden;
		background: var(--app-bg-secondary);
	}

	.shortcuts-panel {
		position: fixed;
		bottom: var(--app-space-lg);
		right: var(--app-space-lg);
		z-index: var(--app-z-tooltip);
	}

	.shortcuts-content {
		padding: var(--app-space-md);
		min-width: 200px;
	}

	.shortcuts-content h4 {
		margin: 0 0 var(--app-space-sm) 0;
		font-size: var(--app-font-size-base);
		color: var(--app-text-primary);
	}

	.shortcut-list {
		display: flex;
		flex-direction: column;
		gap: var(--app-space-xs);
	}

	.shortcut-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		gap: var(--app-space-sm);
		font-size: var(--app-font-size-sm);
	}

	kbd {
		padding: 2px 6px;
		background: var(--app-surface-3);
		border: 1px solid var(--app-border-primary);
		border-radius: var(--app-radius-small);
		font-family: var(--app-font-mono);
		font-size: var(--app-font-size-xs);
		font-weight: var(--app-font-weight-semibold);
	}

	@media (max-width: 1200px) {
		.sidebar-container {
			width: 280px;
		}
	}

	@media (max-width: 768px) {
		.sidebar-container {
			width: 240px;
		}
		.shortcuts-panel {
			bottom: var(--app-space-sm);
			right: var(--app-space-sm);
		}
	}

	@media (max-width: 480px) {
		.sidebar-container {
			position: absolute;
			top: 0;
			left: 0;
			height: 100%;
			z-index: 200;
			transform: translateX(-100%);
		}

		.sidebar-container:not(.collapsed) {
			transform: translateX(0);
		}

		.right-sidebar {
			left: auto;
			right: 0;
			transform: translateX(100%);
		}

		.right-sidebar:not(.collapsed) {
			transform: translateX(0);
		}
	}

	:deep(.el-button) {
		border-radius: var(--app-radius-medium);
		transition: all var(--app-duration-normal) var(--app-ease-out);
	}

	:deep(.el-button:hover) {
		transform: translateY(-1px);
	}

	:deep(.el-button--primary) {
		background: var(--app-primary-gradient);
		border-color: var(--app-primary);
	}

	:deep(.el-button--primary.is-plain) {
		background: transparent;
		color: var(--app-primary);
		border-color: var(--app-primary);
	}

	:deep(.el-button--primary.is-plain:hover) {
		background: var(--app-primary);
		color: white;
	}
</style>
